{"meta": {"instanceId": "god-digital-marketing-automation"}, "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 6 * * *"}]}}, "id": "1", "name": "Daily Content Trigger", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"jsCode": "const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\nconst today = new Date().getDay();\nconst themes = {\n  monday: { theme: 'Motivation Monday', focus: 'inspirational quotes, success stories' },\n  tuesday: { theme: 'Tutorial Tuesday', focus: 'educational content, how-to guides' },\n  wednesday: { theme: 'Wisdom Wednesday', focus: 'industry insights, expert advice' },\n  thursday: { theme: 'Throwback Thursday', focus: 'company milestones, case studies' },\n  friday: { theme: 'Feature Friday', focus: 'tool showcases, team spotlights' },\n  saturday: { theme: 'Social Saturday', focus: 'community engagement, polls' },\n  sunday: { theme: 'Success Sunday', focus: 'testimonials, results' }\n};\nreturn [{ json: { day: days[today], ...themes[days[today]], agency: 'God Digital Marketing' }}];"}, "id": "2", "name": "Content Theme Generator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 300]}, {"parameters": {"requestMethod": "POST", "url": "https://api.groq.com/openai/v1/chat/completions", "authentication": "headerAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "model", "value": "llama-3.1-70b-versatile"}, {"name": "messages", "value": "=[{\"role\": \"system\", \"content\": \"You are a professional social media content creator for God Digital Marketing. Create engaging content that drives leads.\"}, {\"role\": \"user\", \"content\": \"Create social media content for {{ $json.theme }}. Focus: {{ $json.focus }}. Generate: 1) Facebook post (150 chars), 2) Instagram caption (100 chars + hashtags), 3) LinkedIn post (200 chars), 4) Twitter post (250 chars). Include strong CTAs for lead generation.\"}]"}, {"name": "max_tokens", "value": "1500"}]}}, "id": "3", "name": "Groq Text Generation", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [680, 300], "credentials": {"httpHeaderAuth": {"id": "groq-credentials", "name": "Groq API"}}}], "connections": {"Daily Content Trigger": {"main": [[{"node": "Content Theme Generator", "type": "main", "index": 0}]]}, "Content Theme Generator": {"main": [[{"node": "Groq Text Generation", "type": "main", "index": 0}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 1, "updatedAt": "2024-01-01T00:00:00.000Z", "versionId": "1"}