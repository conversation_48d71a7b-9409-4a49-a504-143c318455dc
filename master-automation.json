{"name": "God Digital Marketing - Master Social Media Automation", "nodes": [{"parameters": {"rule": {"interval": [{"field": "cronExpression", "expression": "0 6 * * *"}]}}, "id": "schedule-trigger", "name": "Daily Content Schedule", "type": "n8n-nodes-base.cron", "typeVersion": 1, "position": [180, 300]}, {"parameters": {"jsCode": "// Get current day and determine content theme\nconst days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];\nconst today = new Date().getDay();\nconst currentDay = days[today];\n\nconst contentThemes = {\n  monday: {\n    theme: \"Motivation Monday\",\n    type: \"Inspirational business quotes with custom graphics\",\n    focus: \"motivation, success stories, mindset\",\n    cta: \"What motivates you in business? Share below!\"\n  },\n  tuesday: {\n    theme: \"Tutorial Tuesday\", \n    type: \"Educational how-to content\",\n    focus: \"marketing tips, strategies, tutorials\",\n    cta: \"Need help with this? Book a free consultation!\"\n  },\n  wednesday: {\n    theme: \"Wisdom Wednesday\",\n    type: \"Industry insights and trends\",\n    focus: \"expert advice, data-driven facts\",\n    cta: \"What industry trend interests you most?\"\n  },\n  thursday: {\n    theme: \"Throwback Thursday\",\n    type: \"Company milestones and achievements\",\n    focus: \"success stories, transformations\",\n    cta: \"Share your business transformation story!\"\n  },\n  friday: {\n    theme: \"Feature Friday\",\n    type: \"Tool and service showcases\",\n    focus: \"client spotlights, behind-the-scenes\",\n    cta: \"Want to be featured? Contact us!\"\n  },\n  saturday: {\n    theme: \"Social Saturday\",\n    type: \"Interactive polls and questions\",\n    focus: \"community engagement, UGC\",\n    cta: \"Join our community discussion!\"\n  },\n  sunday: {\n    theme: \"Success Sunday\",\n    type: \"Weekly recap and testimonials\",\n    focus: \"testimonials, call-to-action\",\n    cta: \"Ready to grow your business? Let's talk!\"\n  }\n};\n\nreturn [{\n  json: {\n    day: currentDay,\n    ...contentThemes[currentDay],\n    timestamp: new Date().toISOString(),\n    agency: \"God Digital Marketing\"\n  }\n}];"}, "id": "content-planner", "name": "Content Theme Planner", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [380, 300]}, {"parameters": {"authentication": "headerAuth", "requestMethod": "POST", "url": "https://api.groq.com/openai/v1/chat/completions", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "model", "value": "llama-3.1-70b-versatile"}, {"name": "messages", "value": "=[{\"role\": \"system\", \"content\": \"You are a professional social media content creator for God Digital Marketing agency. Create engaging, high-converting social media content that drives leads and engagement.\"}, {\"role\": \"user\", \"content\": \"Create engaging social media content for God Digital Marketing agency.\\nTheme: {{ $json.theme }}\\nType: {{ $json.type }}\\nFocus: {{ $json.focus }}\\nCall-to-action: {{ $json.cta }}\\n\\nGenerate 5 platform-specific posts:\\n1. Facebook post (max 2200 chars)\\n2. Instagram caption (max 2200 chars, include hashtags)\\n3. LinkedIn post (max 3000 chars, professional tone)\\n4. Twitter/X post (max 280 chars)\\n5. TikTok description (max 150 chars, trending style)\\n\\nInclude relevant hashtags and ensure each post drives engagement and leads.\"}]"}, {"name": "max_tokens", "value": "2000"}, {"name": "temperature", "value": "0.7"}]}}, "id": "groq-text-generator", "name": "Groq Text Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [580, 200], "credentials": {"httpHeaderAuth": {"id": "groq-api-key", "name": "Groq API Key"}}}, {"parameters": {"authentication": "headerAuth", "requestMethod": "POST", "url": "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "inputs", "value": "=Professional marketing graphic for {{ $('content-planner').item.json.theme }}, modern flat design, corporate colors blue and white, clean typography, high quality, 4K resolution, social media optimized, God Digital Marketing branding, {{ $('content-planner').item.json.focus }}, professional business aesthetic"}, {"name": "parameters", "value": "{\"num_inference_steps\": 20, \"guidance_scale\": 7.5}"}]}}, "id": "image-generator", "name": "Stable Diffusion Image Generator", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [580, 400], "credentials": {"httpHeaderAuth": {"id": "huggingface-api-key", "name": "HuggingFace API Key"}}}, {"parameters": {"jsCode": "// Parse and format content for different platforms\nconst textContent = $('groq-text-generator').item.json.choices[0].message.content;\nconst imageData = $('image-generator').item.json;\nconst themeData = $('content-planner').item.json;\n\n// Extract platform-specific content\nconst platforms = {\n  facebook: extractContent(textContent, '1. Facebook post'),\n  instagram: extractContent(textContent, '2. Instagram caption'),\n  linkedin: extractContent(textContent, '3. LinkedIn post'),\n  twitter: extractContent(textContent, '4. Twitter/X post'),\n  tiktok: extractContent(textContent, '5. TikTok description')\n};\n\nfunction extractContent(text, marker) {\n  const lines = text.split('\\n');\n  const startIndex = lines.findIndex(line => line.includes(marker));\n  if (startIndex === -1) return \"Default content for \" + marker;\n  \n  let content = [];\n  for (let i = startIndex + 1; i < lines.length; i++) {\n    if (lines[i].match(/^\\d+\\./)) break; // Next numbered item\n    if (lines[i].trim()) content.push(lines[i]);\n  }\n  return content.join('\\n').trim();\n}\n\n// Generate hashtags based on theme\nconst hashtags = generateHashtags(themeData.theme, themeData.focus);\n\nfunction generateHashtags(theme, focus) {\n  const baseHashtags = ['#GodDigitalMarketing', '#DigitalMarketing', '#BusinessGrowth', '#MarketingTips'];\n  const themeHashtags = {\n    'Motivation Monday': ['#MotivationMonday', '#BusinessMotivation', '#Entrepreneurship', '#Success'],\n    'Tutorial Tuesday': ['#TutorialTuesday', '#MarketingTutorial', '#LearnMarketing', '#HowTo'],\n    'Wisdom Wednesday': ['#WisdomWednesday', '#MarketingWisdom', '#BusinessInsights', '#TrendAlert'],\n    'Throwback Thursday': ['#ThrowbackThursday', '#CompanyMilestone', '#SuccessStory', '#Growth'],\n    'Feature Friday': ['#FeatureFriday', '#ClientSpotlight', '#BehindTheScenes', '#TeamWork'],\n    'Social Saturday': ['#SocialSaturday', '#Community', '#Engagement', '#Discussion'],\n    'Success Sunday': ['#SuccessSunday', '#TestimonialTuesday', '#Results', '#ROI']\n  };\n  \n  return [...baseHashtags, ...(themeHashtags[theme] || [])].join(' ');\n}\n\n// Optimal posting times\nconst postingTimes = {\n  facebook: '13:00',\n  instagram: '11:00', \n  linkedin: '08:00',\n  twitter: '12:00',\n  tiktok: '18:00'\n};\n\nreturn [{\n  json: {\n    content: platforms,\n    hashtags: hashtags,\n    image: imageData,\n    theme: themeData,\n    postingTimes: postingTimes,\n    generatedAt: new Date().toISOString()\n  }\n}];"}, "id": "content-formatter", "name": "Content Formatter & Optimizer", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [780, 300]}, {"parameters": {"resource": "page", "operation": "post", "pageId": "={{ $env.FACEBOOK_PAGE_ID }}", "content": "={{ $json.content.facebook }}\n\n{{ $json.hashtags }}", "additionalFields": {"scheduling": {"scheduledPublishTime": "={{ Date.parse($json.postingTimes.facebook) }}"}}}, "id": "facebook-publisher", "name": "Facebook Publisher", "type": "n8n-nodes-base.facebookGraph", "typeVersion": 1, "position": [980, 100], "credentials": {"facebookGraphApi": {"id": "facebook-credentials", "name": "Facebook Graph API"}}}, {"parameters": {"resource": "post", "operation": "create", "mediaUrl": "={{ $json.image.url }}", "caption": "={{ $json.content.instagram }}\n\n{{ $json.hashtags }}", "additionalFields": {}}, "id": "instagram-publisher", "name": "Instagram Publisher", "type": "n8n-nodes-base.instagram", "typeVersion": 1, "position": [980, 200], "credentials": {"instagramBasicDisplayApi": {"id": "instagram-credentials", "name": "Instagram Basic Display API"}}}, {"parameters": {"resource": "post", "operation": "create", "text": "={{ $json.content.linkedin }}\n\n{{ $json.hashtags }}", "additionalFields": {"visibility": "PUBLIC"}}, "id": "linkedin-publisher", "name": "LinkedIn Publisher", "type": "n8n-nodes-base.linkedIn", "typeVersion": 1, "position": [980, 300], "credentials": {"linkedInOAuth2Api": {"id": "linkedin-credentials", "name": "LinkedIn OAuth2 API"}}}, {"parameters": {"resource": "tweet", "operation": "create", "text": "={{ $json.content.twitter }}\n\n{{ $json.hashtags }}"}, "id": "twitter-publisher", "name": "Twitter/X Publisher", "type": "n8n-nodes-base.twitter", "typeVersion": 2, "position": [980, 400], "credentials": {"twitterOAuth2Api": {"id": "twitter-credentials", "name": "Twitter OAuth2 API"}}}, {"parameters": {"requestMethod": "POST", "url": "https://hooks.zapier.com/hooks/catch/{{ $env.ZAPIER_WEBHOOK_ID }}/", "options": {"headers": {"Content-Type": "application/json"}}, "bodyParametersUi": {"parameter": [{"name": "platform", "value": "analytics"}, {"name": "content", "value": "={{ JSON.stringify($json) }}"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}]}}, "id": "analytics-tracker", "name": "Analytics & Performance Tracker", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.1, "position": [1180, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "error-check", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "string", "operation": "notExists"}}], "combinator": "and"}, "options": {}}, "id": "error-handler", "name": "Error Handler & Quality Check", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [580, 500]}, {"parameters": {"resource": "message", "operation": "sendMessage", "chatId": "={{ $env.TELEGRAM_ADMIN_CHAT_ID }}", "text": "🚨 God Digital Marketing Automation Alert\n\nWorkflow Error Detected:\nTime: {{ new Date().toISOString() }}\nError: {{ $json.error || 'Unknown error' }}\nNode: {{ $json.node || 'Unknown node' }}\n\nPlease check the workflow immediately.", "additionalFields": {"parseMode": "<PERSON><PERSON>"}}, "id": "error-notification", "name": "Error Notification", "type": "n8n-nodes-base.telegram", "typeVersion": 1.1, "position": [780, 600], "credentials": {"telegramApi": {"id": "telegram-bot-credentials", "name": "Telegram Bot API"}}}], "pinData": {}, "connections": {"Daily Content Schedule": {"main": [[{"node": "Content Theme Planner", "type": "main", "index": 0}]]}, "Content Theme Planner": {"main": [[{"node": "Groq Text Generator", "type": "main", "index": 0}, {"node": "Stable Diffusion Image Generator", "type": "main", "index": 0}]]}, "Groq Text Generator": {"main": [[{"node": "Content Formatter & Optimizer", "type": "main", "index": 0}]]}, "Stable Diffusion Image Generator": {"main": [[{"node": "Content Formatter & Optimizer", "type": "main", "index": 0}]]}, "Content Formatter & Optimizer": {"main": [[{"node": "Facebook Publisher", "type": "main", "index": 0}, {"node": "Instagram Publisher", "type": "main", "index": 0}, {"node": "LinkedIn Publisher", "type": "main", "index": 0}, {"node": "Twitter/X Publisher", "type": "main", "index": 0}, {"node": "Error Handler & Quality Check", "type": "main", "index": 0}]]}, "Facebook Publisher": {"main": [[{"node": "Analytics & Performance Tracker", "type": "main", "index": 0}]]}, "Instagram Publisher": {"main": [[{"node": "Analytics & Performance Tracker", "type": "main", "index": 0}]]}, "LinkedIn Publisher": {"main": [[{"node": "Analytics & Performance Tracker", "type": "main", "index": 0}]]}, "Twitter/X Publisher": {"main": [[{"node": "Analytics & Performance Tracker", "type": "main", "index": 0}]]}, "Error Handler & Quality Check": {"main": [[{"node": "Analytics & Performance Tracker", "type": "main", "index": 0}], [{"node": "Error Notification", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1.0.0", "meta": {"templateCredsSetupCompleted": true, "instanceId": "god-digital-marketing-automation"}, "id": "master-automation", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "social-media-automation", "name": "Social Media Automation"}]}